import * as React from "react";
import Svg, { Path } from "react-native-svg";

export const EmptyClassesIcon = (props: any) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={141}
    height={140}
    fill="none"
    {...props}
  >
    <Path
      fill="#e6f9fc"
      // fill: "color(display-p3 .902 .9765 .9882)",
      fillOpacity={1}
      d="M26.83 63.514s19.386-.776 23.575-32.641c3.72-28.3 36.409-15.419 45.858-4.138 12.241 14.613 5.715 39.752 21.55 42.927 15.835 3.176 5.494 38.484-18.542 35.104-29.9-4.204-22.666 13.268-31.642 20.664-6.441 5.307-32.855.198-33.735-19.069-.74-16.213-7.58-16.161-12.427-18-6.991-2.65-11.396-21.853 5.363-24.847Z"
    />
    <Path
      d="M60.5 51c-6.55 0-11.875 5.325-11.875 11.875 0 6.425 5.025 11.625 11.575 11.85.2-.025.4-.025.55 0h.175c6.4-.225 11.425-5.425 11.45-11.85C72.375 56.325 67.05 51 60.5 51ZM73.198 81.375c-6.975-4.65-18.35-4.65-25.375 0-3.175 2.125-4.925 5-4.925 8.075 0 3.075 1.75 5.925 4.9 8.025 3.5 2.35 8.1 3.525 12.7 3.525s9.2-1.175 12.7-3.525c3.15-2.125 4.9-4.975 4.9-8.075-.025-3.075-1.75-5.925-4.9-8.025ZM87.973 64.35c.4 4.85-3.05 9.1-7.825 9.675h-.125c-.15 0-.3 0-.425.05-2.425.125-4.65-.65-6.325-2.075 2.575-2.3 4.05-5.75 3.75-9.5a11.602 11.602 0 0 0-1.925-5.45c.95-.475 2.05-.775 3.175-.875 4.9-.425 9.275 3.225 9.7 8.175Z"
      fill="#00bfe0"
      fillOpacity={1}
    />
    <Path
      d="M92.977 87.475c-.2 2.425-1.75 4.525-4.35 5.95-2.5 1.375-5.65 2.025-8.775 1.95 1.8-1.625 2.85-3.65 3.05-5.8.25-3.1-1.225-6.075-4.175-8.45-1.675-1.325-3.625-2.375-5.75-3.15 5.525-1.6 12.475-.525 16.75 2.925 2.3 1.85 3.475 4.175 3.25 6.575Z"
      fillOpacity={1}
      fill="#00bfe0"
    />
  </Svg>
);
